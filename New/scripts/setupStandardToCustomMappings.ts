#!/usr/bin/env tsx
/**
 * Setup Standard-to-Custom Field Mappings Script
 *
 * This script runs the auto-discovery system to identify and create
 * standard-to-custom field mappings in the database. This is required
 * for AP standard fields (email, phone, etc.) to be synchronized to
 * CC custom fields during webhook processing.
 *
 * Usage:
 *   pnpm tsx scripts/setupStandardToCustomMappings.ts [options]
 *
 * Options:
 *   --dry-run          Show what would be discovered without creating mappings
 *   --force-recreate   Recreate existing mappings
 *   --confidence=0.7   Set confidence threshold (0-1)
 *
 * @fileoverview Auto-discovery setup script for standard-to-custom mappings
 * @version 1.0.0
 * @since 2024-08-04
 */

import { autoDiscoverAndSetupMappings, getMappingStatus } from "@/processors/customFields/management/standardMappingManager";
import { getStandardToCustomMappings } from "@/processors/customFields/database/operations";

// Simple console logging for standalone script
const logInfo = (message: string, data?: any) => {
	console.log(`[INFO] ${message}`, data ? JSON.stringify(data, null, 2) : '');
};

const logError = (message: string, error?: any) => {
	console.error(`[ERROR] ${message}`, error);
};

const logWarn = (message: string, data?: any) => {
	console.warn(`[WARN] ${message}`, data ? JSON.stringify(data, null, 2) : '');
};

interface ScriptOptions {
	dryRun: boolean;
	forceRecreate: boolean;
	confidenceThreshold: number;
	help: boolean;
}

/**
 * Parse command line arguments
 */
function parseArgs(): ScriptOptions {
	const args = process.argv.slice(2);
	const options: ScriptOptions = {
		dryRun: false,
		forceRecreate: false,
		confidenceThreshold: 0.7,
		help: false,
	};

	for (const arg of args) {
		if (arg === "--dry-run") {
			options.dryRun = true;
		} else if (arg === "--force-recreate") {
			options.forceRecreate = true;
		} else if (arg.startsWith("--confidence=")) {
			const value = parseFloat(arg.split("=")[1]);
			if (!isNaN(value) && value >= 0 && value <= 1) {
				options.confidenceThreshold = value;
			} else {
				console.error("Invalid confidence value. Must be between 0 and 1.");
				process.exit(1);
			}
		} else if (arg === "--help" || arg === "-h") {
			options.help = true;
		} else {
			console.error(`Unknown option: ${arg}`);
			process.exit(1);
		}
	}

	return options;
}

/**
 * Display help information
 */
function showHelp(): void {
	console.log(`
Setup Standard-to-Custom Field Mappings

This script discovers and creates mappings between AP standard fields (email, phone, etc.)
and CC custom fields. This enables synchronization of AP standard field values to CC
custom fields during webhook processing.

Usage:
  pnpm tsx scripts/setupStandardToCustomMappings.ts [options]

Options:
  --dry-run              Show what would be discovered without creating mappings
  --force-recreate       Recreate existing mappings (use with caution)
  --confidence=0.7       Set confidence threshold for auto-discovery (0-1)
  --help, -h             Show this help message

Examples:
  # Dry run to see what would be discovered
  pnpm tsx scripts/setupStandardToCustomMappings.ts --dry-run

  # Create mappings with default settings
  pnpm tsx scripts/setupStandardToCustomMappings.ts

  # Create mappings with higher confidence threshold
  pnpm tsx scripts/setupStandardToCustomMappings.ts --confidence=0.8

  # Force recreate all mappings
  pnpm tsx scripts/setupStandardToCustomMappings.ts --force-recreate
`);
}

/**
 * Display current mapping status
 */
async function displayStatus(): Promise<void> {
	try {
		console.log("\n📊 Current Mapping Status:");
		console.log("=" .repeat(50));

		const status = await getMappingStatus();
		console.log(`Feature Enabled: ${status.enabled ? "✅ Yes" : "❌ No"}`);
		console.log(`Existing Mappings: ${status.existingMappings}`);
		console.log(`CC Custom Fields Available: ${status.ccFieldsAvailable}`);
		console.log(`AP Standard Fields: ${status.apStandardFields}`);

		if (status.existingMappings > 0) {
			console.log("\n📋 Existing Mappings:");
			const mappings = await getStandardToCustomMappings();
			for (const mapping of mappings) {
				console.log(`  • ${mapping.apStandardField} → ${mapping.ccCustomField} (ID: ${mapping.ccFieldId})`);
			}
		}
	} catch (error) {
		logError("Failed to get mapping status", error);
		console.error("❌ Failed to get mapping status:", error);
	}
}

/**
 * Main script execution
 */
async function main(): Promise<void> {
	const options = parseArgs();

	if (options.help) {
		showHelp();
		return;
	}

	console.log("🔧 Standard-to-Custom Field Mapping Setup");
	console.log("=" .repeat(50));

	// Display current status
	await displayStatus();

	if (options.dryRun) {
		console.log("\n🔍 Running in DRY RUN mode - no changes will be made");
	}

	console.log(`\n⚙️  Configuration:`);
	console.log(`  • Confidence Threshold: ${options.confidenceThreshold}`);
	console.log(`  • Force Recreate: ${options.forceRecreate ? "Yes" : "No"}`);
	console.log(`  • Dry Run: ${options.dryRun ? "Yes" : "No"}`);

	try {
		console.log("\n🚀 Starting auto-discovery...");

		const result = await autoDiscoverAndSetupMappings({
			autoCreate: !options.dryRun,
			confidenceThreshold: options.confidenceThreshold,
			forceRecreate: options.forceRecreate,
			dryRun: options.dryRun,
			includeMediumConfidence: true,
		});

		console.log("\n📊 Discovery Results:");
		console.log("=" .repeat(30));
		console.log(`Status: ${result.success ? "✅ Success" : "❌ Failed"}`);
		console.log(`Message: ${result.message}`);

		if (result.details?.discoveryResult) {
			const discovery = result.details.discoveryResult;
			console.log(`\nFields Analyzed: ${discovery.statistics.fieldsAnalyzed}`);
			console.log(`High Confidence Matches: ${discovery.statistics.highConfidenceMatches}`);
			console.log(`Medium Confidence Matches: ${discovery.statistics.mediumConfidenceMatches}`);
			console.log(`Suggested Mappings: ${discovery.suggestedMappings.length}`);

			if (discovery.suggestedMappings.length > 0) {
				console.log("\n📋 Discovered Mappings:");
				for (const mapping of discovery.suggestedMappings) {
					console.log(`  • ${mapping.apStandardField} → ${mapping.ccCustomField}`);
					if (mapping.notes) {
						console.log(`    Notes: ${mapping.notes}`);
					}
				}
			}
		}

		if (!options.dryRun) {
			console.log(`\nMappings Created: ${result.mappingsCreated}`);
			console.log(`Mappings Skipped: ${result.mappingsSkipped}`);
		}

		if (result.warnings.length > 0) {
			console.log("\n⚠️  Warnings:");
			for (const warning of result.warnings) {
				console.log(`  • ${warning}`);
			}
		}

		if (result.errors.length > 0) {
			console.log("\n❌ Errors:");
			for (const error of result.errors) {
				console.log(`  • ${error}`);
			}
		}

		// Display updated status if mappings were created
		if (!options.dryRun && result.mappingsCreated > 0) {
			console.log("\n📊 Updated Status:");
			await displayStatus();
		}

		if (options.dryRun && result.details?.discoveryResult?.suggestedMappings && result.details.discoveryResult.suggestedMappings.length > 0) {
			console.log("\n💡 Next Steps:");
			console.log("  Run without --dry-run to create the discovered mappings:");
			console.log("  pnpm tsx scripts/setupStandardToCustomMappings.ts");
		}

	} catch (error) {
		logError("Script execution failed", error);
		console.error("\n❌ Script failed:", error);
		process.exit(1);
	}
}

// Execute the script
main().catch((error) => {
	console.error("Unhandled error:", error);
	process.exit(1);
});
