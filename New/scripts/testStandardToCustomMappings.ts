#!/usr/bin/env tsx
/**
 * Test Standard-to-Custom Field Mappings
 *
 * This script tests the configuration-based standard-to-custom field mapping
 * system to ensure it works correctly with the current CC custom fields.
 *
 * Usage:
 *   pnpm tsx scripts/testStandardToCustomMappings.ts
 *
 * @fileoverview Test script for configuration-based standard-to-custom mappings
 * @version 1.0.0
 * @since 2024-08-04
 */

import { resolveStandardToCustomMappings, STANDARD_TO_CUSTOM_MAPPINGS } from "@/config/standardToCustomMappings";
import { mapStandardFieldsToCustomFields } from "@/processors/apWebhook/fieldMapper";
import apiClient from "@apiClient";
import type { GetAPContactType } from "@type";

/**
 * Test contact data with email and phone
 */
const testContact: GetAPContactType = {
	id: "test-contact-123",
	email: "<EMAIL>",
	phone: "+1234567890",
	firstName: "<PERSON>",
	lastName: "<PERSON><PERSON>",
	name: "<PERSON>",
	tags: [],
	customFields: [],
	source: "test",
	dateCreated: "2024-08-04T12:00:00Z",
	dateUpdated: "2024-08-04T12:00:00Z",
	locationId: "test-location",
	contactType: "lead",
	// Add other required fields with default values
	address1: "",
	city: "",
	state: "",
	country: "",
	postalCode: "",
	website: "",
	timezone: "",
	dnd: false,
	dndSettings: {},
	inboundDndSettings: {},
	tags: [],
	customFields: [],
	followers: [],
	attributions: [],
	businessId: "",
	companyName: "",
	dateOfBirth: "",
	ssn: "",
	gender: "",
};

/**
 * Main test function
 */
async function main(): Promise<void> {
	console.log("🧪 Testing Standard-to-Custom Field Mappings");
	console.log("=" .repeat(50));

	try {
		// Test 1: Check configuration
		console.log("\n📋 Configuration Test:");
		console.log(`Total configured mappings: ${STANDARD_TO_CUSTOM_MAPPINGS.length}`);
		
		for (const mapping of STANDARD_TO_CUSTOM_MAPPINGS) {
			const status = mapping.enabled !== false ? "✅ Enabled" : "❌ Disabled";
			console.log(`  • ${mapping.apStandardField}: ${status}`);
			console.log(`    CC field names: ${mapping.ccCustomFieldNames.join(", ")}`);
		}

		// Test 2: Fetch CC custom fields and resolve mappings
		console.log("\n🔍 CC Custom Fields Resolution Test:");
		const ccFields = await apiClient.cc.ccCustomfieldReq.all();
		console.log(`Found ${ccFields.length} CC custom fields`);

		const resolvedMappings = resolveStandardToCustomMappings(ccFields);
		console.log(`Resolved ${resolvedMappings.length} mappings`);

		if (resolvedMappings.length === 0) {
			console.log("❌ No mappings resolved! Check if CC custom fields exist with matching names.");
			return;
		}

		console.log("\n📋 Resolved Mappings:");
		for (const resolved of resolvedMappings) {
			console.log(`  ✅ ${resolved.apStandardField} → ${resolved.ccField.name} (ID: ${resolved.ccField.id}, Type: ${resolved.ccField.type})`);
		}

		// Test 3: Test actual field mapping with test contact
		console.log("\n🔄 Field Mapping Test:");
		console.log("Testing with contact data:");
		console.log(`  Email: ${testContact.email}`);
		console.log(`  Phone: ${testContact.phone}`);
		console.log(`  First Name: ${testContact.firstName}`);
		console.log(`  Last Name: ${testContact.lastName}`);

		const mappingResult = await mapStandardFieldsToCustomFields(testContact);
		
		console.log("\n📊 Mapping Results:");
		console.log(`  Mapped Count: ${mappingResult.mappedCount}`);
		console.log(`  Custom Field Updates: ${mappingResult.customFieldUpdates.length}`);
		console.log(`  Warnings: ${mappingResult.warnings.length}`);
		console.log(`  Errors: ${mappingResult.errors.length}`);

		if (mappingResult.customFieldUpdates.length > 0) {
			console.log("\n📋 Custom Field Updates:");
			for (const update of mappingResult.customFieldUpdates) {
				console.log(`  • Field ID ${update.field.id}: ${JSON.stringify(update.values)}`);
			}
		}

		if (mappingResult.warnings.length > 0) {
			console.log("\n⚠️  Warnings:");
			for (const warning of mappingResult.warnings) {
				console.log(`  • ${warning}`);
			}
		}

		if (mappingResult.errors.length > 0) {
			console.log("\n❌ Errors:");
			for (const error of mappingResult.errors) {
				console.log(`  • ${error}`);
			}
		}

		// Test 4: Check specific email and phone mappings
		console.log("\n🎯 Email & Phone Mapping Check:");
		const emailMapping = resolvedMappings.find(m => m.apStandardField === "email");
		const phoneMapping = resolvedMappings.find(m => m.apStandardField === "phone");

		if (emailMapping) {
			console.log(`✅ Email mapping found: ${emailMapping.ccField.name} (ID: ${emailMapping.ccField.id})`);
		} else {
			console.log("❌ Email mapping not found - this will cause the original issue!");
		}

		if (phoneMapping) {
			console.log(`✅ Phone mapping found: ${phoneMapping.ccField.name} (ID: ${phoneMapping.ccField.id})`);
		} else {
			console.log("❌ Phone mapping not found - this will cause the original issue!");
		}

		// Summary
		console.log("\n🎉 Test Summary:");
		if (emailMapping && phoneMapping && mappingResult.mappedCount > 0) {
			console.log("✅ All tests passed! Email and phone synchronization should work.");
		} else {
			console.log("❌ Some tests failed. Email/phone synchronization may not work properly.");
		}

	} catch (error) {
		console.error("\n❌ Test failed:", error);
		process.exit(1);
	}
}

// Execute the test
main().catch((error) => {
	console.error("Unhandled error:", error);
	process.exit(1);
});
