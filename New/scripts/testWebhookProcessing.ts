#!/usr/bin/env tsx
/**
 * Test Webhook Processing with Email/Phone Sync
 *
 * This script simulates the webhook processing flow to test if email and phone
 * fields are correctly synchronized from AP to CC custom fields.
 *
 * Usage:
 *   pnpm tsx scripts/testWebhookProcessing.ts
 *
 * @fileoverview Test webhook processing with standard-to-custom field mapping
 * @version 1.0.0
 * @since 2024-08-04
 */

import { mapApContactToCcPatient } from "@/processors/apWebhook/fieldMapper";
import type { GetAPContactType } from "@type";

/**
 * Test contact data matching the webhook payload mentioned in the issue
 */
const testContact: GetAPContactType = {
	id: "KGU5VQTetxTHFAYS5GH9", // Contact ID from the webhook payload
	email: "<EMAIL>", // Email from the webhook payload
	phone: "+8601650483486", // Phone from the webhook payload
	firstName: "Test",
	lastName: "User",
	name: "Test User",
	tags: [],
	customFields: [],
	source: "webhook_test",
	dateCreated: "2024-08-04T12:00:00Z",
	dateUpdated: "2024-08-04T12:00:00Z",
	locationId: "test-location",
	contactType: "lead",
	// Add other required fields with default values
	address1: "",
	city: "",
	state: "",
	country: "",
	postalCode: "",
	website: "",
	timezone: "",
	dnd: false,
	dndSettings: {},
	inboundDndSettings: {},
	followers: [],
	attributions: [],
	businessId: "",
	companyName: "",
	dateOfBirth: "",
	ssn: "",
	gender: "",
};

/**
 * Main test function
 */
async function main(): Promise<void> {
	console.log("🧪 Testing Webhook Processing with Email/Phone Sync");
	console.log("=" .repeat(55));

	console.log("\n📋 Test Contact Data:");
	console.log(`  Contact ID: ${testContact.id}`);
	console.log(`  Email: ${testContact.email}`);
	console.log(`  Phone: ${testContact.phone}`);
	console.log(`  Name: ${testContact.firstName} ${testContact.lastName}`);

	try {
		console.log("\n🔄 Processing AP Contact to CC Patient Mapping...");
		
		// This is the main function called during webhook processing
		const fieldMapping = await mapApContactToCcPatient(testContact);

		console.log("\n📊 Field Mapping Results:");
		console.log(`  Mapped Fields: ${fieldMapping.mappedFields}`);
		console.log(`  Warnings: ${fieldMapping.warnings.length}`);
		console.log(`  Unmapped Fields: ${fieldMapping.unmappedFields.length}`);

		// Check if CC patient data was created
		if (fieldMapping.ccPatientData) {
			console.log("\n✅ CC Patient Data Created:");
			console.log(`  Email: ${fieldMapping.ccPatientData.email || 'Not set'}`);
			console.log(`  Phone Mobile: ${fieldMapping.ccPatientData.phoneMobile || 'Not set'}`);
			console.log(`  First Name: ${fieldMapping.ccPatientData.firstName || 'Not set'}`);
			console.log(`  Last Name: ${fieldMapping.ccPatientData.lastName || 'Not set'}`);
			
			// Check custom fields - this is the key part for the fix
			if (fieldMapping.ccPatientData.customFields && fieldMapping.ccPatientData.customFields.length > 0) {
				console.log(`\n🎯 Custom Fields (${fieldMapping.ccPatientData.customFields.length}):`);
				for (const customField of fieldMapping.ccPatientData.customFields) {
					console.log(`  • Field ID ${customField.field.id}:`);
					for (const value of customField.values) {
						if (value.id) {
							console.log(`    - Option ID: ${value.id}`);
						} else {
							console.log(`    - Value: ${value.value}`);
						}
					}
				}
			} else {
				console.log("\n❌ No custom fields created - this indicates the issue is NOT fixed!");
			}
		}

		// Check standard-to-custom mapping results specifically
		if (fieldMapping.standardToCustomResult) {
			const stcResult = fieldMapping.standardToCustomResult;
			console.log("\n🔗 Standard-to-Custom Mapping Results:");
			console.log(`  Mapped Count: ${stcResult.mappedCount}`);
			console.log(`  Custom Field Updates: ${stcResult.customFieldUpdates.length}`);
			console.log(`  Warnings: ${stcResult.warnings.length}`);
			console.log(`  Errors: ${stcResult.errors.length}`);

			if (stcResult.customFieldUpdates.length > 0) {
				console.log("\n📋 Standard Field → Custom Field Updates:");
				for (const update of stcResult.customFieldUpdates) {
					console.log(`  • Field ID ${update.field.id} (${update.field.name}):`);
					for (const value of update.values) {
						console.log(`    - ${value.value || `Option ID: ${value.id}`}`);
					}
				}
			}

			if (stcResult.errors.length > 0) {
				console.log("\n❌ Standard-to-Custom Mapping Errors:");
				for (const error of stcResult.errors) {
					console.log(`  • ${error}`);
				}
			}
		}

		if (fieldMapping.warnings.length > 0) {
			console.log("\n⚠️  Warnings:");
			for (const warning of fieldMapping.warnings) {
				console.log(`  • ${warning}`);
			}
		}

		// Final assessment
		console.log("\n🎯 Issue Fix Assessment:");
		const hasEmailCustomField = fieldMapping.ccPatientData?.customFields?.some(cf => 
			cf.values.some(v => v.value === testContact.email)
		);
		const hasPhoneCustomField = fieldMapping.ccPatientData?.customFields?.some(cf => 
			cf.values.some(v => v.value === testContact.phone)
		);

		if (hasEmailCustomField && hasPhoneCustomField) {
			console.log("✅ SUCCESS: Both email and phone are being mapped to CC custom fields!");
			console.log("✅ The original issue has been FIXED!");
		} else if (hasEmailCustomField || hasPhoneCustomField) {
			console.log("⚠️  PARTIAL: Only one of email/phone is being mapped to CC custom fields");
			console.log(`   Email mapped: ${hasEmailCustomField ? 'Yes' : 'No'}`);
			console.log(`   Phone mapped: ${hasPhoneCustomField ? 'Yes' : 'No'}`);
		} else {
			console.log("❌ FAILURE: Neither email nor phone are being mapped to CC custom fields");
			console.log("❌ The original issue is NOT fixed!");
		}

	} catch (error) {
		console.error("\n❌ Test failed:", error);
		process.exit(1);
	}
}

// Execute the test
main().catch((error) => {
	console.error("Unhandled error:", error);
	process.exit(1);
});
