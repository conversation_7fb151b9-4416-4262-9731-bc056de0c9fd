#!/usr/bin/env tsx
/**
 * Create Basic Standard-to-Custom Field Mappings
 *
 * This script directly creates the essential standard-to-custom field mappings
 * for email and phone fields without going through the complex auto-discovery
 * system. This is a simpler approach to fix the immediate issue.
 *
 * Usage:
 *   pnpm tsx scripts/createBasicMappings.ts
 *
 * @fileoverview Direct creation of basic standard-to-custom mappings
 * @version 1.0.0
 * @since 2024-08-04
 */

import { getDb, dbSchema } from "@database";
import apiClient from "@apiClient";
import type { GetCCCustomField } from "@type";
import { eq } from "drizzle-orm";

/**
 * Basic mappings to create
 */
const BASIC_MAPPINGS = [
	{
		apStandardField: "email",
		ccCustomFieldNames: ["email", "Email", "EMAIL", "email_address", "emailAddress"],
		notes: "AP standard field 'email' maps to CC custom field for email"
	},
	{
		apStandardField: "phone",
		ccCustomFieldNames: ["phone", "Phone", "PHONE", "phone_number", "phoneNumber", "mobile", "Mobile"],
		notes: "AP standard field 'phone' maps to CC custom field for phone"
	}
];

/**
 * Find CC custom field by name (case-insensitive)
 */
function findCcCustomField(ccFields: GetCCCustomField[], possibleNames: string[]): GetCCCustomField | null {
	for (const name of possibleNames) {
		const field = ccFields.find(f => 
			f.name.toLowerCase() === name.toLowerCase() || 
			f.label?.toLowerCase() === name.toLowerCase()
		);
		if (field) {
			return field;
		}
	}
	return null;
}

/**
 * Check if mapping already exists
 */
async function mappingExists(apStandardField: string): Promise<boolean> {
	try {
		const db = getDb();
		const result = await db
			.select()
			.from(dbSchema.customFields)
			.where(eq(dbSchema.customFields.apStandardField, apStandardField))
			.limit(1);
		
		return result.length > 0;
	} catch (error) {
		console.error(`Error checking if mapping exists for ${apStandardField}:`, error);
		return false;
	}
}

/**
 * Create standard-to-custom mapping
 */
async function createMapping(apStandardField: string, ccField: GetCCCustomField, notes: string): Promise<boolean> {
	try {
		const db = getDb();
		
		const mappingData = {
			apId: null, // No AP custom field ID for standard fields
			ccId: ccField.id,
			name: apStandardField, // Use AP standard field name
			label: ccField.label,
			type: ccField.type,
			apConfig: null, // No AP custom field config
			ccConfig: ccField,
			mappingType: "standard_to_custom" as const,
			apStandardField: apStandardField,
			ccStandardField: null,
		};

		await db
			.insert(dbSchema.customFields)
			.values(mappingData)
			.onConflictDoUpdate({
				target: [dbSchema.customFields.ccId],
				set: {
					apId: mappingData.apId,
					name: mappingData.name,
					label: mappingData.label,
					type: mappingData.type,
					apConfig: mappingData.apConfig,
					ccConfig: mappingData.ccConfig,
					mappingType: mappingData.mappingType,
					apStandardField: mappingData.apStandardField,
					ccStandardField: mappingData.ccStandardField,
					updatedAt: new Date(),
				},
			});

		console.log(`✅ Created mapping: ${apStandardField} → ${ccField.name} (ID: ${ccField.id})`);
		return true;
	} catch (error) {
		console.error(`❌ Failed to create mapping for ${apStandardField}:`, error);
		return false;
	}
}

/**
 * Display current mappings
 */
async function displayCurrentMappings(): Promise<void> {
	try {
		const db = getDb();
		const mappings = await db
			.select({
				apStandardField: dbSchema.customFields.apStandardField,
				ccCustomField: dbSchema.customFields.name,
				ccFieldId: dbSchema.customFields.ccId,
			})
			.from(dbSchema.customFields)
			.where(eq(dbSchema.customFields.mappingType, "standard_to_custom"));

		if (mappings.length === 0) {
			console.log("📋 No existing standard-to-custom mappings found");
		} else {
			console.log("📋 Existing standard-to-custom mappings:");
			for (const mapping of mappings) {
				console.log(`  • ${mapping.apStandardField} → ${mapping.ccCustomField} (ID: ${mapping.ccFieldId})`);
			}
		}
	} catch (error) {
		console.error("❌ Failed to retrieve existing mappings:", error);
	}
}

/**
 * Main script execution
 */
async function main(): Promise<void> {
	console.log("🔧 Creating Basic Standard-to-Custom Field Mappings");
	console.log("=" .repeat(60));

	try {
		// Display current mappings
		console.log("\n📊 Current Status:");
		await displayCurrentMappings();

		// Fetch CC custom fields
		console.log("\n🔍 Fetching CliniCore custom fields...");
		const ccFields = await apiClient.cc.ccCustomfieldReq.all();
		console.log(`Found ${ccFields.length} CC custom fields`);

		if (ccFields.length === 0) {
			console.log("❌ No CC custom fields found. Cannot create mappings.");
			return;
		}

		let createdCount = 0;
		let skippedCount = 0;

		// Process each basic mapping
		for (const mapping of BASIC_MAPPINGS) {
			console.log(`\n🔍 Processing ${mapping.apStandardField}...`);

			// Check if mapping already exists
			const exists = await mappingExists(mapping.apStandardField);
			if (exists) {
				console.log(`⏭️  Mapping for ${mapping.apStandardField} already exists, skipping`);
				skippedCount++;
				continue;
			}

			// Find matching CC custom field
			const ccField = findCcCustomField(ccFields, mapping.ccCustomFieldNames);
			if (!ccField) {
				console.log(`❌ No matching CC custom field found for ${mapping.apStandardField}`);
				console.log(`   Searched for: ${mapping.ccCustomFieldNames.join(", ")}`);
				continue;
			}

			console.log(`✅ Found matching CC field: ${ccField.name} (ID: ${ccField.id}, Type: ${ccField.type})`);

			// Create the mapping
			const success = await createMapping(mapping.apStandardField, ccField, mapping.notes);
			if (success) {
				createdCount++;
			}
		}

		// Display results
		console.log("\n📊 Results:");
		console.log("=" .repeat(30));
		console.log(`✅ Mappings created: ${createdCount}`);
		console.log(`⏭️  Mappings skipped: ${skippedCount}`);

		// Display updated mappings
		if (createdCount > 0) {
			console.log("\n📋 Updated mappings:");
			await displayCurrentMappings();
		}

		console.log("\n🎉 Basic mapping setup completed!");
		console.log("\n💡 Next steps:");
		console.log("  1. Test the webhook processing with the new mappings");
		console.log("  2. Send a test webhook payload to verify email/phone synchronization");

	} catch (error) {
		console.error("\n❌ Script failed:", error);
		process.exit(1);
	}
}

// Execute the script
main().catch((error) => {
	console.error("Unhandled error:", error);
	process.exit(1);
});
