/**
 * AutoPatient to CliniCore Field Mapping
 *
 * Provides field mapping utilities for converting AutoPatient contact data
 * to CliniCore patient format. Handles standard field mappings, custom
 * field processing, and data type conversions with comprehensive logging
 * and error handling.
 *
 * @fileoverview Field mapping utilities for AP to CC contact synchronization
 * @version 1.0.0
 * @since 2024-07-27
 */

import type { GetAPContactType, PostCCPatientType } from "@type";
import { logDebug, logInfo, logWarn } from "@/utils/logger";
import { convertApStandardValueToCc } from "@/processors/patientCustomFields/valueConverters";
import type { CCCustomFieldValue, StandardFieldMappingResult } from "@/processors/patientCustomFields/types";
import type { FieldMappingResult } from "./types";
import { resolveStandardToCustomMappings } from "@/config/standardToCustomMappings";
import apiClient from "@apiClient";

/**
 * Map AutoPatient contact data to CliniCore patient format
 *
 * Converts AutoPatient contact fields to CliniCore patient fields using
 * the standard field mapping configuration. Handles both direct field
 * mappings and custom field processing.
 * Now works with full contact data from AP API instead of webhook payload.
 *
 * **Mapping Strategy:**
 * 1. Map standard AP contact fields to CC patient fields
 * 2. Handle name field variations (firstName, lastName, fullName)
 * 3. Process address information from location data
 * 4. Map AP standard fields to CC custom fields (if configured)
 * 5. Identify custom fields that need separate processing
 * 6. Apply data type conversions and validation
 *
 * @param apContact - Full AutoPatient contact data from API
 * @returns Field mapping result with CC patient data and processing details
 *
 * @example
 * ```typescript
 * const mappingResult = await mapApContactToCcPatient(apContact);
 * console.log(`Mapped ${mappingResult.mappedFields} standard fields`);
 *
 * // Use the mapped data for CC API call
 * const ccPatient = await apiClient.cc.createPatient(mappingResult.ccPatientData);
 * ```
 */
export async function mapApContactToCcPatient(
	apContact: GetAPContactType,
): Promise<FieldMappingResult> {
	logDebug(`Starting AP to CC field mapping for contact ID: ${apContact.id}`);

	const ccPatientData: Partial<PostCCPatientType> = {};
	const warnings: string[] = [];
	const unmappedFields: string[] = [];
	let mappedFields = 0;

	// Map standard contact fields
	mappedFields += mapStandardFields(apContact, ccPatientData);

	// Map name fields with priority handling
	mappedFields += mapNameFields(apContact, ccPatientData, warnings);

	// Map address fields from location data
	mappedFields += mapAddressFields(apContact, ccPatientData);

	// Map AP standard fields to CC custom fields
	const standardToCustomResult = await mapStandardFieldsToCustomFields(apContact);
	if (standardToCustomResult.customFieldUpdates.length > 0) {
		// Convert CCCustomFieldValue[] to CCFieldPayload[] format for PostCCPatientType
		const customFieldPayloads = standardToCustomResult.customFieldUpdates.map(ccValue => ({
			field: { id: ccValue.field.id.toString() },
			values: ccValue.values.map(v =>
				v.id ? { id: v.id.toString() } : { value: v.value || "" }
			)
		}));
		ccPatientData.customFields = customFieldPayloads;
		mappedFields += standardToCustomResult.mappedCount;
		warnings.push(...standardToCustomResult.warnings);

		if (standardToCustomResult.errors.length > 0) {
			logWarn("Standard-to-custom field mapping errors:", standardToCustomResult.errors);
		}
	}

	// Process custom fields (from full contact data)
	processCustomFields(apContact, unmappedFields);

	logInfo(
		`AP to CC field mapping completed: ${mappedFields} fields mapped, ` +
			`${warnings.length} warnings, ${unmappedFields.length} unmapped fields`,
	);

	if (warnings.length > 0) {
		logWarn("Field mapping warnings:", warnings);
	}

	return {
		ccPatientData: ccPatientData as PostCCPatientType,
		warnings,
		mappedFields,
		unmappedFields,
		standardToCustomResult, // Include standard-to-custom mapping results
	};
}

/**
 * Map standard AutoPatient contact fields to CliniCore patient fields
 *
 * Handles direct field mappings using the standard field mapping configuration.
 * Maps fields like email, phone, country, etc.
 * Now works with full contact data from AP API.
 *
 * @param apContact - Full AutoPatient contact data from API
 * @param ccPatientData - CliniCore patient data being built
 * @returns Number of fields successfully mapped
 */
function mapStandardFields(
	apContact: GetAPContactType,
	ccPatientData: Partial<PostCCPatientType>,
): number {
	let mappedCount = 0;

	// Map email field
	if (apContact.email) {
		ccPatientData.email = apContact.email;
		mappedCount++;
		logDebug(`Mapped email: ${apContact.email}`);
	}

	// Map phone field (use phone field directly)
	if (apContact.phone) {
		ccPatientData.phoneMobile = apContact.phone;
		mappedCount++;
		logDebug(`Mapped phone: ${apContact.phone}`);
	}

	// Map active status (default to true for new contacts)
	ccPatientData.active = true;
	mappedCount++;

	logDebug(`Mapped ${mappedCount} standard fields`);
	return mappedCount;
}

/**
 * Map name fields with priority handling
 *
 * Handles name field mapping with priority: first_name/last_name > full_name.
 * Attempts to parse full_name if individual name fields are not available.
 *
 * @param apContact - AutoPatient contact data
 * @param ccPatientData - CliniCore patient data being built
 * @param warnings - Array to collect mapping warnings
 * @returns Number of fields successfully mapped
 */
function mapNameFields(
	apContact: GetAPContactType,
	ccPatientData: Partial<PostCCPatientType>,
	warnings: string[],
): number {
	let mappedCount = 0;

	// Priority 1: Use firstName and lastName if available
	if (apContact.firstName || apContact.lastName) {
		if (apContact.firstName) {
			ccPatientData.firstName = apContact.firstName;
			mappedCount++;
			logDebug(`Mapped firstName: ${apContact.firstName}`);
		}

		if (apContact.lastName) {
			ccPatientData.lastName = apContact.lastName;
			mappedCount++;
			logDebug(`Mapped lastName: ${apContact.lastName}`);
		}
	}
	// Priority 2: Parse name if individual names not available
	else if (apContact.name) {
		const nameParts = apContact.name.trim().split(/\s+/);
		if (nameParts.length >= 2) {
			ccPatientData.firstName = nameParts[0];
			ccPatientData.lastName = nameParts.slice(1).join(" ");
			mappedCount += 2;
			logDebug(
				`Parsed name: ${ccPatientData.firstName} ${ccPatientData.lastName}`,
			);
		} else if (nameParts.length === 1) {
			ccPatientData.firstName = nameParts[0];
			mappedCount++;
			warnings.push(`Only first name available from name: ${apContact.name}`);
			logDebug(`Mapped single name as firstName: ${nameParts[0]}`);
		}
	} else {
		warnings.push("No name fields available in AP contact data");
		logWarn("No name fields found in contact data");
	}

	return mappedCount;
}

/**
 * Map address fields from location data
 *
 * Extracts address information from the AP contact's location object
 * and maps it to CC patient address format.
 *
 * @param apContact - AutoPatient contact data
 * @param ccPatientData - CliniCore patient data being built
 * @returns Number of fields successfully mapped
 */
function mapAddressFields(
	apContact: GetAPContactType,
	ccPatientData: Partial<PostCCPatientType>,
): number {
	let mappedCount = 0;

	// Map address fields from AP contact data
	const address: {
		id?: number;
		street?: string;
		city?: string;
		state?: string;
		zipCode?: string;
		country?: string;
		[key: string]: unknown;
	} = {};

	if (apContact.address1) {
		address.street = apContact.address1;
		mappedCount++;
	}

	if (apContact.city) {
		address.city = apContact.city;
		mappedCount++;
	}

	if (apContact.state) {
		address.state = apContact.state;
		mappedCount++;
	}

	if (apContact.postalCode) {
		address.zipCode = apContact.postalCode;
		mappedCount++;
	}

	if (apContact.country) {
		address.country = apContact.country;
		mappedCount++;
	}

	// Only add address if we have at least some address data
	if (mappedCount > 0) {
		// Add required fields for CC address format
		address.id = 1;
		address.label = null;
		address.name = null;
		address.streetNumber = "";
		address.postalCode = address.zipCode || "";
		address.primary = 1;

		ccPatientData.addresses = [address];
		logDebug(`Mapped address with ${mappedCount} fields`);
	}

	return mappedCount;
}

/**
 * Process custom fields from webhook data
 *
 * Identifies custom fields in the webhook payload that need separate processing.
 * These fields will be handled by the custom field synchronization system.
 *
 * @param apContact - AutoPatient contact data
 * @param unmappedFields - Array to collect unmapped field names
 */
function processCustomFields(
	apContact: GetAPContactType,
	unmappedFields: string[],
): void {
	// Check for custom fields in the AP contact data
	if (apContact.customFields && Array.isArray(apContact.customFields)) {
		const customFieldNames = apContact.customFields.map((field) => field.id);
		if (customFieldNames.length > 0) {
			unmappedFields.push(...customFieldNames);
			logDebug(
				`Found ${customFieldNames.length} custom fields for separate processing`,
			);
		}
	}

	// Check for additional fields that might be custom fields
	// (fields not in the standard AP contact structure)
	const standardFields = new Set([
		"contact_id",
		"first_name",
		"last_name",
		"full_name",
		"email",
		"phone",
		"tags",
		"country",
		"date_created",
		"full_address",
		"contact_type",
		"location",
		"workflow",
		"triggerData",
		"contact",
		"attributionSource",
		"customData",
	]);

	for (const [fieldName, fieldValue] of Object.entries(apContact)) {
		if (
			!standardFields.has(fieldName) &&
			fieldValue !== undefined &&
			fieldValue !== ""
		) {
			unmappedFields.push(fieldName);
			logDebug(`Found potential custom field: ${fieldName}`);
		}
	}
}

/**
 * Map AP standard fields to CC custom fields
 *
 * Handles mapping of AutoPatient standard fields (email, phone, etc.) to
 * CliniCore custom fields based on configured mappings. This enables
 * synchronization of AP standard field values to CC custom fields.
 *
 * @param apContact - AutoPatient contact data
 * @returns Standard field mapping result with custom field updates
 *
 * @example
 * ```typescript
 * const result = await mapStandardFieldsToCustomFields(apContact);
 * if (result.customFieldUpdates.length > 0) {
 *   ccPatientData.customFields = result.customFieldUpdates;
 * }
 * ```
 *
 * @since 2.0.0
 */
export async function mapStandardFieldsToCustomFields(
	apContact: GetAPContactType,
): Promise<StandardFieldMappingResult> {
	const result: StandardFieldMappingResult = {
		mappedCount: 0,
		customFieldUpdates: [],
		warnings: [],
		errors: [],
		processedFields: [],
	};

	try {
		logDebug("Starting AP standard to CC custom field mapping", {
			contactId: apContact.id,
		});

		// Get CC custom fields and resolve mappings from configuration
		const ccFields = await apiClient.cc.ccCustomfieldReq.all();
		const resolvedMappings = resolveStandardToCustomMappings(ccFields);

		if (resolvedMappings.length === 0) {
			logDebug("No standard-to-custom field mappings resolved from configuration");
			return result;
		}

		logDebug(`Resolved ${resolvedMappings.length} standard-to-custom field mappings from configuration`);

		// Process each resolved mapping
		for (const resolvedMapping of resolvedMappings) {
			const { apStandardField, ccField } = resolvedMapping;

			// Extract the value from AP contact data
			const apValue = extractApStandardFieldValue(apContact, apStandardField);

			const fieldResult: {
				apStandardField: string;
				ccFieldId: number;
				success: boolean;
				originalValue: unknown;
				convertedValue?: CCCustomFieldValue;
				error?: string;
				warnings?: string[];
			} = {
				apStandardField,
				ccFieldId: ccField.id,
				success: false,
				originalValue: apValue,
			};

			// Skip if no value present
			if (apValue === null || apValue === undefined || apValue === "") {
				logDebug(`Skipping empty value for standard field: ${apStandardField}`);
				result.processedFields.push(fieldResult);
				continue;
			}

			try {
				// Convert AP standard field value to CC custom field format
				const conversionResult = convertApStandardValueToCc(
					apStandardField,
					apValue,
					ccField,
				);

				if (conversionResult.success && conversionResult.convertedValue) {
					const ccValue = conversionResult.convertedValue as CCCustomFieldValue;
					result.customFieldUpdates.push(ccValue);
					result.mappedCount++;
					fieldResult.success = true;
					fieldResult.convertedValue = ccValue;

					logDebug(`Successfully mapped standard field to custom field`, {
						apStandardField,
						ccFieldId: ccField.id,
						originalValue: apValue,
					});

					// Add any conversion warnings
					if (conversionResult.warnings) {
						result.warnings.push(...conversionResult.warnings);
						fieldResult.warnings = conversionResult.warnings;
					}
				} else {
					const error = conversionResult.error || "Conversion failed";
					result.errors.push(`Failed to convert ${apStandardField}: ${error}`);
					fieldResult.error = error;

					logWarn(`Failed to convert standard field value`, {
						apStandardField,
						ccFieldId: ccField.id,
						error,
					});
				}
			} catch (error) {
				const errorMessage = error instanceof Error ? error.message : String(error);
				result.errors.push(`Error processing ${apStandardField}: ${errorMessage}`);
				fieldResult.error = errorMessage;

				logWarn(`Error processing standard field mapping`, {
					apStandardField,
					ccFieldId: ccField.id,
					error: errorMessage,
				});
			}

			result.processedFields.push(fieldResult);
		}

		logInfo(`Standard-to-custom field mapping completed`, {
			contactId: apContact.id,
			mappedCount: result.mappedCount,
			totalMappings: resolvedMappings.length,
			warningCount: result.warnings.length,
			errorCount: result.errors.length,
		});

	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		result.errors.push(`Failed to resolve standard-to-custom mappings: ${errorMessage}`);

		logWarn(`Error in standard-to-custom field mapping`, {
			contactId: apContact.id,
			error: errorMessage,
		});
	}

	return result;
}

/**
 * Extract value from AP contact data for a given standard field
 *
 * @param apContact - AutoPatient contact data
 * @param fieldName - Standard field name to extract
 * @returns Field value or null if not found
 */
function extractApStandardFieldValue(
	apContact: GetAPContactType,
	fieldName: string,
): unknown {
	// Map of standard field names to contact properties
	const fieldMap: Record<string, keyof GetAPContactType> = {
		email: "email",
		phone: "phone",
		firstName: "firstName",
		lastName: "lastName",
		name: "name",
		dateOfBirth: "dateOfBirth",
		gender: "gender",
		source: "source",
		country: "country",
		address1: "address1",
		city: "city",
		state: "state",
		postalCode: "postalCode",
		companyName: "companyName",
		tags: "tags",
	};

	const contactProperty = fieldMap[fieldName];
	if (contactProperty && contactProperty in apContact) {
		return apContact[contactProperty];
	}

	logDebug(`Standard field not found in contact data: ${fieldName}`);
	return null;
}
