/**
 * Standard-to-Custom Field Mappings Configuration
 *
 * Simple configuration array for mapping AP standard fields to CC custom fields.
 * This approach allows easy maintenance and updates without requiring database setup.
 *
 * **How it works:**
 * 1. Define mappings in the STANDARD_TO_CUSTOM_MAPPINGS array below
 * 2. The webhook processor will automatically find matching CC custom fields
 * 3. Values from AP standard fields will be synchronized to CC custom fields
 *
 * **Adding new mappings:**
 * Simply add a new entry to the array with the AP standard field name and
 * possible CC custom field names to match against.
 *
 * @fileoverview Configuration-based standard-to-custom field mappings
 * @version 1.0.0
 * @since 2024-08-04
 */

import type { GetCCCustomField } from "@type";

/**
 * Configuration for a standard-to-custom field mapping
 */
export interface StandardToCustomMappingConfig {
	/** AP standard field name (e.g., "email", "phone") */
	apStandardField: string;
	/** Possible CC custom field names to match against (case-insensitive) */
	ccCustomFieldNames: string[];
	/** Optional notes about this mapping */
	notes?: string;
	/** Whether this mapping is enabled (default: true) */
	enabled?: boolean;
}

/**
 * Resolved mapping with CC field configuration
 */
export interface ResolvedStandardToCustomMapping {
	/** AP standard field name */
	apStandardField: string;
	/** Matched CC custom field */
	ccField: GetCCCustomField;
	/** Original configuration */
	config: StandardToCustomMappingConfig;
}

/**
 * Standard-to-Custom Field Mappings Configuration
 *
 * Add new mappings here to enable synchronization of AP standard fields
 * to CC custom fields. The system will automatically find matching CC
 * custom fields based on the names provided.
 *
 * **Field Name Matching:**
 * - Case-insensitive matching against both field name and label
 * - First match wins, so order matters
 * - Use most specific names first, then more general ones
 *
 * **Examples:**
 * ```typescript
 * {
 *   apStandardField: "email",
 *   ccCustomFieldNames: ["email", "Email", "email_address", "emailAddress"],
 *   notes: "Patient email address"
 * }
 * ```
 */
export const STANDARD_TO_CUSTOM_MAPPINGS: StandardToCustomMappingConfig[] = [
	{
		apStandardField: "email",
		ccCustomFieldNames: [
			"email",
			"Email", 
			"EMAIL",
			"email_address",
			"emailAddress",
			"Email Address",
			"patient_email",
			"patientEmail"
		],
		notes: "AP standard field 'email' maps to CC custom field for email address",
		enabled: true,
	},
	{
		apStandardField: "phone",
		ccCustomFieldNames: [
			"phone",
			"Phone",
			"PHONE",
			"phone_number",
			"phoneNumber",
			"Phone Number",
			"mobile",
			"Mobile",
			"cell",
			"Cell",
			"telephone",
			"Telephone",
			"patient_phone",
			"patientPhone"
		],
		notes: "AP standard field 'phone' maps to CC custom field for phone number",
		enabled: true,
	},
	{
		apStandardField: "firstName",
		ccCustomFieldNames: [
			"firstName",
			"first_name",
			"First Name",
			"fname",
			"given_name",
			"givenName"
		],
		notes: "AP standard field 'firstName' maps to CC custom field for first name",
		enabled: true,
	},
	{
		apStandardField: "lastName",
		ccCustomFieldNames: [
			"lastName",
			"last_name",
			"Last Name",
			"lname",
			"surname",
			"family_name",
			"familyName"
		],
		notes: "AP standard field 'lastName' maps to CC custom field for last name",
		enabled: true,
	},
	// Add more mappings here as needed
	// {
	//   apStandardField: "dateOfBirth",
	//   ccCustomFieldNames: ["dob", "date_of_birth", "birth_date", "birthDate"],
	//   notes: "AP standard field 'dateOfBirth' maps to CC custom field for date of birth",
	//   enabled: true,
	// },
];

/**
 * Find CC custom field by name (case-insensitive)
 *
 * Searches through CC custom fields to find a match based on field name or label.
 * Uses case-insensitive matching and returns the first match found.
 *
 * @param ccFields - Array of CC custom fields to search
 * @param possibleNames - Array of possible field names to match against
 * @returns Matching CC custom field or null if not found
 */
export function findCcCustomFieldByName(
	ccFields: GetCCCustomField[],
	possibleNames: string[]
): GetCCCustomField | null {
	for (const name of possibleNames) {
		const field = ccFields.find(f => 
			f.name.toLowerCase() === name.toLowerCase() || 
			f.label?.toLowerCase() === name.toLowerCase()
		);
		if (field) {
			return field;
		}
	}
	return null;
}

/**
 * Resolve standard-to-custom mappings against available CC custom fields
 *
 * Takes the configuration mappings and resolves them against the actual
 * CC custom fields available in the system. Only returns mappings where
 * a matching CC custom field was found.
 *
 * @param ccFields - Array of available CC custom fields
 * @returns Array of resolved mappings with CC field configurations
 */
export function resolveStandardToCustomMappings(
	ccFields: GetCCCustomField[]
): ResolvedStandardToCustomMapping[] {
	const resolvedMappings: ResolvedStandardToCustomMapping[] = [];

	for (const config of STANDARD_TO_CUSTOM_MAPPINGS) {
		// Skip disabled mappings
		if (config.enabled === false) {
			continue;
		}

		// Find matching CC custom field
		const ccField = findCcCustomFieldByName(ccFields, config.ccCustomFieldNames);
		if (ccField) {
			resolvedMappings.push({
				apStandardField: config.apStandardField,
				ccField,
				config,
			});
		}
	}

	return resolvedMappings;
}

/**
 * Get enabled standard field names
 *
 * Returns an array of AP standard field names that have enabled mappings.
 * Useful for validation and debugging.
 *
 * @returns Array of enabled AP standard field names
 */
export function getEnabledStandardFields(): string[] {
	return STANDARD_TO_CUSTOM_MAPPINGS
		.filter(mapping => mapping.enabled !== false)
		.map(mapping => mapping.apStandardField);
}

/**
 * Check if a standard field has a mapping configured
 *
 * @param apStandardField - AP standard field name to check
 * @returns True if mapping is configured and enabled
 */
export function hasStandardFieldMapping(apStandardField: string): boolean {
	return STANDARD_TO_CUSTOM_MAPPINGS.some(
		mapping => mapping.apStandardField === apStandardField && mapping.enabled !== false
	);
}
