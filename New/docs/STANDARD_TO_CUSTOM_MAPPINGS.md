# Standard-to-Custom Field Mappings

## Overview

This document explains how the standard-to-custom field mapping system works and how to add new mappings for synchronizing AutoPatient (AP) standard fields to CliniCore (CC) custom fields.

## Problem Solved

Previously, email and phone fields from AP webhook payloads were not being synchronized to CC custom fields. This was because the new implementation required database-configured mappings, but no mappings were set up.

## Solution

We implemented a **configuration-based approach** that allows easy maintenance of field mappings without requiring database setup or complex auto-discovery systems.

## How It Works

1. **Configuration File**: `src/config/standardToCustomMappings.ts` contains an array of mapping configurations
2. **Runtime Resolution**: During webhook processing, the system fetches CC custom fields and resolves mappings based on field names
3. **Automatic Synchronization**: AP standard field values are automatically converted and synchronized to matching CC custom fields

## Current Mappings

The following AP standard fields are currently mapped to CC custom fields:

| AP Standard Field | CC Custom Field Names (searched) | Status |
|-------------------|-----------------------------------|---------|
| `email` | email, Email, EMAIL, email_address, emailAddress, Email Address, patient_email, patientEmail | ✅ Enabled |
| `phone` | phone, Phone, PHONE, phone_number, phoneNumber, Phone Number, mobile, Mobile, cell, Cell, telephone, Telephone, patient_phone, patientPhone | ✅ Enabled |
| `firstName` | firstName, first_name, First Name, fname, given_name, givenName | ✅ Enabled |
| `lastName` | lastName, last_name, Last Name, lname, surname, family_name, familyName | ✅ Enabled |

## Adding New Mappings

To add a new standard-to-custom field mapping:

1. **Edit the configuration file**: `src/config/standardToCustomMappings.ts`
2. **Add a new entry** to the `STANDARD_TO_CUSTOM_MAPPINGS` array:

```typescript
{
  apStandardField: "dateOfBirth",
  ccCustomFieldNames: [
    "dob",
    "date_of_birth", 
    "birth_date",
    "birthDate",
    "Date of Birth"
  ],
  notes: "AP standard field 'dateOfBirth' maps to CC custom field for date of birth",
  enabled: true,
}
```

3. **Deploy the changes** - no database updates required!

## Configuration Format

Each mapping configuration has the following structure:

```typescript
interface StandardToCustomMappingConfig {
  /** AP standard field name (e.g., "email", "phone") */
  apStandardField: string;
  /** Possible CC custom field names to match against (case-insensitive) */
  ccCustomFieldNames: string[];
  /** Optional notes about this mapping */
  notes?: string;
  /** Whether this mapping is enabled (default: true) */
  enabled?: boolean;
}
```

## Field Name Matching

The system uses **case-insensitive matching** against both CC custom field `name` and `label` properties:

- **First match wins** - order matters in the `ccCustomFieldNames` array
- **Use specific names first** - put exact matches before general ones
- **Include variations** - consider different naming conventions (camelCase, snake_case, Title Case)

## Examples

### Example 1: Adding Gender Mapping

```typescript
{
  apStandardField: "gender",
  ccCustomFieldNames: [
    "gender",
    "Gender",
    "sex",
    "Sex",
    "patient_gender"
  ],
  notes: "Patient gender/sex information",
  enabled: true,
}
```

### Example 2: Adding Address Mapping

```typescript
{
  apStandardField: "address1",
  ccCustomFieldNames: [
    "address",
    "Address",
    "street_address",
    "streetAddress",
    "address1",
    "Address 1"
  ],
  notes: "Primary street address",
  enabled: true,
}
```

### Example 3: Disabling a Mapping

```typescript
{
  apStandardField: "ssn",
  ccCustomFieldNames: ["ssn", "social_security"],
  notes: "Disabled for privacy compliance",
  enabled: false, // This mapping will be ignored
}
```

## Testing

After adding new mappings, you can test them using the provided test scripts:

```bash
# Test configuration resolution
pnpm tsx scripts/testStandardToCustomMappings.ts

# Test webhook processing (requires Hono context)
pnpm tsx scripts/testWebhookProcessing.ts
```

## Troubleshooting

### Mapping Not Working

1. **Check CC custom field exists**: Verify the custom field exists in CliniCore with one of the configured names
2. **Check field name spelling**: Ensure the field names in the configuration match exactly (case-insensitive)
3. **Check mapping is enabled**: Verify `enabled` is not set to `false`
4. **Check logs**: Look for mapping resolution logs during webhook processing

### Adding More Field Name Variations

If a mapping isn't working, add more field name variations to the `ccCustomFieldNames` array:

```typescript
ccCustomFieldNames: [
  "email",           // exact match
  "Email",           // title case
  "EMAIL",           // uppercase
  "email_address",   // snake_case
  "emailAddress",    // camelCase
  "Email Address",   // title case with space
  "e_mail",          // alternative spelling
  "e-mail",          // alternative spelling with hyphen
]
```

## Benefits of This Approach

1. **Easy Maintenance**: No database setup required
2. **Version Controlled**: Mappings are stored in code and version controlled
3. **Flexible**: Easy to add, modify, or disable mappings
4. **Transparent**: All mappings are visible in the configuration file
5. **Testable**: Can be tested independently of the database

## Migration from Database Approach

This configuration-based approach replaces the previous database-driven auto-discovery system. The benefits include:

- **Simpler setup**: No need to run discovery scripts
- **More reliable**: No dependency on database state
- **Easier debugging**: All mappings are explicit and visible
- **Better performance**: No database queries during webhook processing

## Future Enhancements

Possible future improvements:

1. **Field type validation**: Ensure AP and CC field types are compatible
2. **Value transformation**: Custom value transformation functions for specific fields
3. **Conditional mappings**: Enable/disable mappings based on conditions
4. **Mapping priorities**: Handle cases where multiple CC fields could match
